<template>
  <a-modal
    title="分配跟进人"
    :visible="visible"
    :closable="true"
    :maskClosable="false"
    width="600px"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="assign-content">
      <div class="customer-count">
        为 <span class="count-number">{{ selectedCount }}</span> 个客户分配跟进人
      </div>

      <a-form :model="form" ref="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="跟进人" name="follower" :rules="[{ required: true, message: '请选择跟进人' }]">
          <a-select
            v-model="form.follower"
            placeholder="请输入或选择跟进人，单选"
            show-search
            :filter-option="false"
            :not-found-content="loading ? undefined : null"
            @search="handleSearch"
            allowClear
          >
            <a-spin v-if="loading" slot="notFoundContent" size="small" />
            <a-select-option v-for="item in followerOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>

    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { getUserListAPI, allocateFollowerAPI } from '@/api/customer';

export default {
  name: 'AssignFollower',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectedCustomers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        follower: undefined,
      },
      followerOptions: [],
      loading: false,
      confirmLoading: false,
    };
  },
  computed: {
    selectedCount() {
      return this.selectedCustomers.length;
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm();
        this.loadInitialFollowers();
      }
    },
  },
  created() {
    // 创建防抖搜索函数
    this.debouncedSearch = this.debounce(this.searchFollowers, 300);
  },
  methods: {
    // 防抖函数
    debounce(func, delay) {
      let timeoutId;
      return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
      };
    },

    resetForm() {
      this.form.follower = undefined;
      this.followerOptions = [];
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },

    async loadInitialFollowers() {
      this.loading = true;
      try {
        await this.searchFollowers('');
      } catch (error) {
        console.error('加载跟进人列表失败:', error);
      } finally {
        this.loading = false;
      }
    },

    async searchFollowers(keyword = '') {
      try {
        // 获取租户下的用户列表
        const [userResult] = await getUserListAPI({ nickName: keyword });

        // 合并数据并去重
        const userOptions = (userResult.data || []).map((user) => ({
          value: user.nickName,
          label: user.nickName,
          type: 'user',
        }));

        const uniqueOptions = userOptions.filter(
          (option, index, self) => index === self.findIndex((item) => item.value === option.value)
        );

        this.followerOptions = uniqueOptions;
      } catch (error) {
        console.error('搜索跟进人失败:', error);
        this.$message.error('搜索跟进人失败');
      }
    },

    handleSearch(value) {
      this.loading = true;
      this.debouncedSearch(value).finally(() => {
        this.loading = false;
      });
    },

    async handleConfirm() {
      try {
        if (!this.form.follower) {
          this.$message.warning('请选择跟进人');
          return;
        }

        this.confirmLoading = true;

        // 提取客户ID
        const customerIds = this.selectedCustomers.map((customer) => customer.customerId);

        // 调用分配接口
        const [result] = await allocateFollowerAPI({
          customerIds,
          customerFollower: this.form.follower,
        });

        if (result.success) {
          this.$message.success('分配跟进人成功');
          this.$emit('success');
          this.handleCancel();
        } else {
          this.$message.error(result.msg || '分配跟进人失败');
        }
      } catch (error) {
        console.error('分配跟进人失败:', error);
        this.$message.error('分配跟进人失败');
      } finally {
        this.confirmLoading = false;
      }
    },

    handleCancel() {
      this.$emit('cancel');
    },
  },
};
</script>

<style scoped>
.assign-content {
  padding: 16px 0;
}

.customer-count {
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;
}

.count-number {
  color: #1890ff;
  font-weight: bold;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-footer .ant-btn {
  margin-left: 8px;
}
</style>
