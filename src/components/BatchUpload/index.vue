<template>
  <a-modal
    :title="title"
    :visible="visible"
    :closable="true"
    :maskClosable="false"
    width="50%"
    @cancel="handleBatchCancel"
    :footer="null"
  >
    <a-form :model="batchForm" ref="batchForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row>
        <a-col :span="24">
          <a-form-item label="下载模版:" name="file">
            <slot name="templateBtn">
              <a-button type="link" @click="downExcel">点击下载</a-button>
            </slot>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="上传文件:" name="file">
            <a-upload
              ref="upload"
              :file-list="fileList"
              :multiple="false"
              accept=".xlsx,.xls"
              :disabled="upload.isUploading"
              :before-upload="beforeUpload"
              :custom-request="customRequest"
              @change="handleChangeFile"
            >
              <a-button> <a-icon type="upload" /> 选择文件 </a-button>
            </a-upload>
            <div class="upload-tip">
              <slot name="tip">上传格式支持xlsx、xls文件，{{ maxSizeText }}以内。</slot>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
      <slot name="extraForm" :params="extraData"></slot>
    </a-form>

    <div class="modal-footer">
      <a-button @click="handleBatchCancel" :loading="submitLoading">取 消</a-button>
      <a-button type="primary" @click="handleBatchSubmit" :loading="submitLoading">开始上传</a-button>
    </div>
  </a-modal>
</template>

<script>
import request from '@/utils/system/request';

export default {
  name: 'BatchUpload',
  props: {
    title: {
      type: String,
      default: '批量导入',
    },
    // 上传时附带的额外参数
    extraData: {
      type: Object,
      default: () => ({}),
    },
    // 上传接口
    uploadApi: {
      type: String,
      default: '',
      required: true,
    },
    // 表格模版路径 eg:'/charging-maintenance-ui/static/公司机构导入模板.xlsx'
    templateUrl: {
      type: String,
      default: '',
    },
    maxSize: {
      type: Number,
      default: 2,
    },
    maxSizeText: {
      type: String,
      default: '2G',
    },
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      fileList: [],
      batchForm: {
        file: [],
      },
    };
  },
  computed: {
    upload() {
      return {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        updateAsCode: '',
      };
    },
  },
  methods: {
    open() {
      this.visible = true;
      this.fileList = [];
      this.batchForm.file = [];
    },

    beforeUpload(file) {
      // 检查文件大小
      if (file.size / 1024 / 1024 / 1024 > this.maxSize) {
        this.$message.error(`上传的文件大小不能超过${this.maxSizeText}!`);
        return false;
      }
      return true;
    },

    customRequest(options) {
      // 不自动上传，只是存储文件信息
      this.batchForm.file = [options.file];
      // 设置文件状态为完成，避免显示转圈圈
      options.onSuccess();
      return false;
    },

    handleChangeFile(info) {
      // 只保留最后一个文件，并设置状态为 done
      this.fileList = info.fileList.slice(-1).map((file) => ({
        ...file,
        status: 'done', // 明确设置文件状态为完成，避免显示转圈圈
      }));
      this.batchForm.file = this.fileList.map((file) => file.originFileObj || file);
    },

    downExcel() {
      if (this.templateUrl) {
        window.location.href = this.templateUrl;
      } else {
        this.$message.warning('模板下载地址未配置');
      }
    },

    // 批量配置-提交
    async handleBatchSubmit() {
      if (this.batchForm.file?.length === 0) {
        this.$message.error('请上传文件！');
        return;
      }

      if (this.batchForm.file[0].size / 1024 / 1024 / 1024 > this.maxSize) {
        this.$message.error(`上传的文件大小不能超过${this.maxSizeText}!`);
        return;
      }

      this.submitLoading = true;

      try {
        // 触发开始上传事件，让父组件处理上传逻辑
        this.$emit('beforeUpload', this.batchForm.file[0], this.extraData);
      } catch (error) {
        this.submitLoading = false;
        console.error('上传失败:', error);
      }
    },

    // 执行实际上传
    async doUpload(file, extraParams = {}) {
      const formData = new FormData();
      formData.append('file', file);

      // 添加额外参数到FormData中
      Object.keys(extraParams).forEach((key) => {
        formData.append(key, extraParams[key]);
      });

      try {
        const response = await request({
          url: this.uploadApi,
          method: 'post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        this.handleUploadSuccess(response);
      } catch (error) {
        this.handleUploadError(error);
      }
    },

    // 处理上传成功
    handleUploadSuccess(response) {
      this.submitLoading = false;
      console.log('response===', response);

      // 适配项目统一的响应格式
      const [res] = response;

      if (!res.success) {
        this.$confirm({
          title: '导入失败！',
          content: res.message || res.rtnMsg,
          okText: '重新上传',
          cancelText: '取消',
          onOk: () => {
            this.batchForm.file = [];
            this.fileList = [];
          },
          onCancel: () => {
            this.handleBatchCancel();
            // 如果是部分成功部分失败的情况，也要刷新列表
            if (res.code == '60001') {
              this.$emit('uploadSuccess');
            }
          },
        });
      } else {
        this.handleBatchCancel();
        this.$message.success('导入成功');
        this.$emit('uploadSuccess', res.data);
      }
    },

    // 处理上传失败
    handleUploadError(error) {
      this.submitLoading = false;
      console.log('上传失败:', error);
      this.$message.error(error.rtnMsg || error.message || '上传失败');
    },

    handleBatchCancel() {
      this.visible = false;
      this.submitLoading = false;
      this.batchForm.file = [];
      this.fileList = [];
    },
  },
};
</script>

<style scoped>
.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-footer .ant-btn {
  margin-left: 8px;
}
</style>
